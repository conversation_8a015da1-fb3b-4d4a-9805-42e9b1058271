package com.vellumcodex.toppa

import android.app.Application
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.vellumcodex.toppa.data.VocabEntry
import com.vellumcodex.toppa.data.VocabRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch

class MainViewModel(application: Application) : AndroidViewModel(application) {

    // 取得資料庫和Repository
    private val repository: VocabRepository

    // 當前的主頁面
    private val _screenMain = MutableStateFlow("practice")
    val screenMain: StateFlow<String> = _screenMain

    fun setMainScreen(value: String) {
        _screenMain.value = value
    }

    // 當前的練習模式頁面
    private val _screenPractice = MutableStateFlow("home")
    val screenPractice: StateFlow<String> = _screenPractice

    fun setPracticeScreen(value: String) {
        _screenPractice.value = value
    }

    // 當前是否在練習中
    private val _isPracticing = MutableStateFlow(false)
    val isPracticing: StateFlow<Boolean> = _isPracticing

    fun setPracticing(value: Boolean) {
        _isPracticing.value = value
    }

    // 題組設定
    private val _quizGroup = MutableStateFlow("All")
    val quizGroup: StateFlow<String> = _quizGroup

    private val _quizVocabs = MutableStateFlow<List<VocabEntry>>(emptyList())
    val quizVocabs: StateFlow<List<VocabEntry>> = _quizVocabs

    private var allVocabs: List<VocabEntry> = emptyList()

    init {
        // 初始化 Repository
        val app = getApplication<MainApplication>()
        val vocabDao = app.database.vocabDao()
        repository = VocabRepository(vocabDao)

        // 從資料庫載入資料
        viewModelScope.launch {
            // 首次啟動時，從JSON載入資料到資料庫
            repository.loadVocabsFromJson(getApplication())

            // 監聽資料庫變化
            repository.allVocabs.collect { vocabs ->
                allVocabs = vocabs
                _quizVocabs.value = when (_quizGroup.value) {
                    "All" -> allVocabs
                    else -> allVocabs.filter { it.group == _quizGroup.value }
                }
            }
        }
    }

    fun setQuizGroup(value: String) {
        _quizGroup.value = value

        _quizVocabs.value = when (value) {
            "All" -> allVocabs
            else -> allVocabs.filter { it.group == value }
        }
    }

    private val _quizIndex = MutableStateFlow(0)
    val quizIndex: StateFlow<Int> = _quizIndex

    fun setQuizIndex(value: Int) {
        _quizIndex.value = value
    }

    // 亂序出題
    private val _quizRandom = MutableStateFlow(true)
    val quizRandom: StateFlow<Boolean> = _quizRandom

    fun setQuizRandom(value: Boolean) {
        _quizRandom.value = value
    }

    // 自動下一題
    private val _autoNext = mutableStateOf(true)
    val autoNext: State<Boolean> = _autoNext

    fun setAutoNext(value: Boolean) {
        _autoNext.value = value
    }

    // 自動下一題的延遲時間
    private val _autoNextDelay = mutableIntStateOf(1250)
    val autoNextDelay: State<Int> = _autoNextDelay

    fun setAutoNextDelay(value: Int) {
        _autoNextDelay.intValue = value.coerceIn(100, 10000)
    }
}