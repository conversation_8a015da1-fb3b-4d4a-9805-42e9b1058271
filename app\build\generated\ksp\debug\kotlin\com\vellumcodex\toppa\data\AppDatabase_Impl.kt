package com.vellumcodex.toppa.`data`

import androidx.room.InvalidationTracker
import androidx.room.RoomOpenDelegate
import androidx.room.migration.AutoMigrationSpec
import androidx.room.migration.Migration
import androidx.room.util.TableInfo
import androidx.room.util.TableInfo.Companion.read
import androidx.room.util.dropFtsSyncTriggers
import androidx.sqlite.SQLiteConnection
import androidx.sqlite.execSQL
import javax.`annotation`.processing.Generated
import kotlin.Lazy
import kotlin.String
import kotlin.Suppress
import kotlin.collections.List
import kotlin.collections.Map
import kotlin.collections.MutableList
import kotlin.collections.MutableMap
import kotlin.collections.MutableSet
import kotlin.collections.Set
import kotlin.collections.mutableListOf
import kotlin.collections.mutableMapOf
import kotlin.collections.mutableSetOf
import kotlin.reflect.KClass

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class AppDatabase_Impl : AppDatabase() {
  private val _vocabDao: Lazy<VocabDao> = lazy {
    VocabDao_Impl(this)
  }

  protected override fun createOpenDelegate(): RoomOpenDelegate {
    val _openDelegate: RoomOpenDelegate = object : RoomOpenDelegate(1,
        "f5e08cced1f33134d93db7981e2ad930", "b1ad786a5334f9482a007c2dd086e44d") {
      public override fun createAllTables(connection: SQLiteConnection) {
        connection.execSQL("CREATE TABLE IF NOT EXISTS `vocab_entries` (`id` INTEGER NOT NULL, `vocab` TEXT NOT NULL, `kanji` TEXT NOT NULL, `zhTW` TEXT NOT NULL, `enUS` TEXT NOT NULL, `group` TEXT NOT NULL, `category` TEXT NOT NULL, `pronounce` TEXT NOT NULL, `tag` TEXT NOT NULL, `avoid` TEXT NOT NULL, `remark` TEXT NOT NULL, PRIMARY KEY(`id`))")
        connection.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)")
        connection.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'f5e08cced1f33134d93db7981e2ad930')")
      }

      public override fun dropAllTables(connection: SQLiteConnection) {
        connection.execSQL("DROP TABLE IF EXISTS `vocab_entries`")
      }

      public override fun onCreate(connection: SQLiteConnection) {
      }

      public override fun onOpen(connection: SQLiteConnection) {
        internalInitInvalidationTracker(connection)
      }

      public override fun onPreMigrate(connection: SQLiteConnection) {
        dropFtsSyncTriggers(connection)
      }

      public override fun onPostMigrate(connection: SQLiteConnection) {
      }

      public override fun onValidateSchema(connection: SQLiteConnection):
          RoomOpenDelegate.ValidationResult {
        val _columnsVocabEntries: MutableMap<String, TableInfo.Column> = mutableMapOf()
        _columnsVocabEntries.put("id", TableInfo.Column("id", "INTEGER", true, 1, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVocabEntries.put("vocab", TableInfo.Column("vocab", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVocabEntries.put("kanji", TableInfo.Column("kanji", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVocabEntries.put("zhTW", TableInfo.Column("zhTW", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVocabEntries.put("enUS", TableInfo.Column("enUS", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVocabEntries.put("group", TableInfo.Column("group", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVocabEntries.put("category", TableInfo.Column("category", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVocabEntries.put("pronounce", TableInfo.Column("pronounce", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVocabEntries.put("tag", TableInfo.Column("tag", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVocabEntries.put("avoid", TableInfo.Column("avoid", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        _columnsVocabEntries.put("remark", TableInfo.Column("remark", "TEXT", true, 0, null,
            TableInfo.CREATED_FROM_ENTITY))
        val _foreignKeysVocabEntries: MutableSet<TableInfo.ForeignKey> = mutableSetOf()
        val _indicesVocabEntries: MutableSet<TableInfo.Index> = mutableSetOf()
        val _infoVocabEntries: TableInfo = TableInfo("vocab_entries", _columnsVocabEntries,
            _foreignKeysVocabEntries, _indicesVocabEntries)
        val _existingVocabEntries: TableInfo = read(connection, "vocab_entries")
        if (!_infoVocabEntries.equals(_existingVocabEntries)) {
          return RoomOpenDelegate.ValidationResult(false, """
              |vocab_entries(com.vellumcodex.toppa.data.VocabEntry).
              | Expected:
              |""".trimMargin() + _infoVocabEntries + """
              |
              | Found:
              |""".trimMargin() + _existingVocabEntries)
        }
        return RoomOpenDelegate.ValidationResult(true, null)
      }
    }
    return _openDelegate
  }

  protected override fun createInvalidationTracker(): InvalidationTracker {
    val _shadowTablesMap: MutableMap<String, String> = mutableMapOf()
    val _viewTables: MutableMap<String, Set<String>> = mutableMapOf()
    return InvalidationTracker(this, _shadowTablesMap, _viewTables, "vocab_entries")
  }

  public override fun clearAllTables() {
    super.performClear(false, "vocab_entries")
  }

  protected override fun getRequiredTypeConverterClasses(): Map<KClass<*>, List<KClass<*>>> {
    val _typeConvertersMap: MutableMap<KClass<*>, List<KClass<*>>> = mutableMapOf()
    _typeConvertersMap.put(VocabDao::class, VocabDao_Impl.getRequiredConverters())
    return _typeConvertersMap
  }

  public override fun getRequiredAutoMigrationSpecClasses(): Set<KClass<out AutoMigrationSpec>> {
    val _autoMigrationSpecsSet: MutableSet<KClass<out AutoMigrationSpec>> = mutableSetOf()
    return _autoMigrationSpecsSet
  }

  public override
      fun createAutoMigrations(autoMigrationSpecs: Map<KClass<out AutoMigrationSpec>, AutoMigrationSpec>):
      List<Migration> {
    val _autoMigrations: MutableList<Migration> = mutableListOf()
    return _autoMigrations
  }

  public override fun vocabDao(): VocabDao = _vocabDao.value
}
