package com.vellumcodex.toppa.data

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import kotlinx.coroutines.flow.Flow

@Dao
interface VocabDao {
    @Query("SELECT * FROM vocab_entries")
    fun getAllVocabs(): Flow<List<VocabEntry>>

    @Query("SELECT * FROM vocab_entries")
    suspend fun getAllVocabsList(): List<VocabEntry>

    @Query("SELECT * FROM vocab_entries WHERE `group` = :group")
    fun getVocabsByGroup(group: String): Flow<List<VocabEntry>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(vocabs: List<VocabEntry>)

    @Query("DELETE FROM vocab_entries")
    suspend fun deleteAll()
}