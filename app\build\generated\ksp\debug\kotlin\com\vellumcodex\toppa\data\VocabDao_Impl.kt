package com.vellumcodex.toppa.`data`

import androidx.room.EntityInsertAdapter
import androidx.room.RoomDatabase
import androidx.room.coroutines.createFlow
import androidx.room.util.getColumnIndexOrThrow
import androidx.room.util.performSuspending
import androidx.sqlite.SQLiteStatement
import javax.`annotation`.processing.Generated
import kotlin.Int
import kotlin.String
import kotlin.Suppress
import kotlin.Unit
import kotlin.collections.List
import kotlin.collections.MutableList
import kotlin.collections.mutableListOf
import kotlin.reflect.KClass
import kotlinx.coroutines.flow.Flow

@Generated(value = ["androidx.room.RoomProcessor"])
@Suppress(names = ["UNCHECKED_CAST", "DEPRECATION", "REDUNDANT_PROJECTION", "REMOVAL"])
public class VocabDao_Impl(
  __db: RoomDatabase,
) : VocabDao {
  private val __db: RoomDatabase

  private val __insertAdapterOfVocabEntry: EntityInsertAdapter<VocabEntry>
  init {
    this.__db = __db
    this.__insertAdapterOfVocabEntry = object : EntityInsertAdapter<VocabEntry>() {
      protected override fun createQuery(): String =
          "INSERT OR REPLACE INTO `vocab_entries` (`id`,`vocab`,`kanji`,`zhTW`,`enUS`,`group`,`category`,`pronounce`,`tag`,`avoid`,`remark`) VALUES (?,?,?,?,?,?,?,?,?,?,?)"

      protected override fun bind(statement: SQLiteStatement, entity: VocabEntry) {
        statement.bindLong(1, entity.id.toLong())
        statement.bindText(2, entity.vocab)
        statement.bindText(3, entity.kanji)
        statement.bindText(4, entity.zhTW)
        statement.bindText(5, entity.enUS)
        statement.bindText(6, entity.group)
        statement.bindText(7, entity.category)
        statement.bindText(8, entity.pronounce)
        statement.bindText(9, entity.tag)
        statement.bindText(10, entity.avoid)
        statement.bindText(11, entity.remark)
      }
    }
  }

  public override suspend fun insertAll(vocabs: List<VocabEntry>): Unit = performSuspending(__db,
      false, true) { _connection ->
    __insertAdapterOfVocabEntry.insert(_connection, vocabs)
  }

  public override fun getAllVocabs(): Flow<List<VocabEntry>> {
    val _sql: String = "SELECT * FROM vocab_entries"
    return createFlow(__db, false, arrayOf("vocab_entries")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfVocab: Int = getColumnIndexOrThrow(_stmt, "vocab")
        val _columnIndexOfKanji: Int = getColumnIndexOrThrow(_stmt, "kanji")
        val _columnIndexOfZhTW: Int = getColumnIndexOrThrow(_stmt, "zhTW")
        val _columnIndexOfEnUS: Int = getColumnIndexOrThrow(_stmt, "enUS")
        val _columnIndexOfGroup: Int = getColumnIndexOrThrow(_stmt, "group")
        val _columnIndexOfCategory: Int = getColumnIndexOrThrow(_stmt, "category")
        val _columnIndexOfPronounce: Int = getColumnIndexOrThrow(_stmt, "pronounce")
        val _columnIndexOfTag: Int = getColumnIndexOrThrow(_stmt, "tag")
        val _columnIndexOfAvoid: Int = getColumnIndexOrThrow(_stmt, "avoid")
        val _columnIndexOfRemark: Int = getColumnIndexOrThrow(_stmt, "remark")
        val _result: MutableList<VocabEntry> = mutableListOf()
        while (_stmt.step()) {
          val _item: VocabEntry
          val _tmpId: Int
          _tmpId = _stmt.getLong(_columnIndexOfId).toInt()
          val _tmpVocab: String
          _tmpVocab = _stmt.getText(_columnIndexOfVocab)
          val _tmpKanji: String
          _tmpKanji = _stmt.getText(_columnIndexOfKanji)
          val _tmpZhTW: String
          _tmpZhTW = _stmt.getText(_columnIndexOfZhTW)
          val _tmpEnUS: String
          _tmpEnUS = _stmt.getText(_columnIndexOfEnUS)
          val _tmpGroup: String
          _tmpGroup = _stmt.getText(_columnIndexOfGroup)
          val _tmpCategory: String
          _tmpCategory = _stmt.getText(_columnIndexOfCategory)
          val _tmpPronounce: String
          _tmpPronounce = _stmt.getText(_columnIndexOfPronounce)
          val _tmpTag: String
          _tmpTag = _stmt.getText(_columnIndexOfTag)
          val _tmpAvoid: String
          _tmpAvoid = _stmt.getText(_columnIndexOfAvoid)
          val _tmpRemark: String
          _tmpRemark = _stmt.getText(_columnIndexOfRemark)
          _item =
              VocabEntry(_tmpId,_tmpVocab,_tmpKanji,_tmpZhTW,_tmpEnUS,_tmpGroup,_tmpCategory,_tmpPronounce,_tmpTag,_tmpAvoid,_tmpRemark)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun getAllVocabsList(): List<VocabEntry> {
    val _sql: String = "SELECT * FROM vocab_entries"
    return performSuspending(__db, true, false) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfVocab: Int = getColumnIndexOrThrow(_stmt, "vocab")
        val _columnIndexOfKanji: Int = getColumnIndexOrThrow(_stmt, "kanji")
        val _columnIndexOfZhTW: Int = getColumnIndexOrThrow(_stmt, "zhTW")
        val _columnIndexOfEnUS: Int = getColumnIndexOrThrow(_stmt, "enUS")
        val _columnIndexOfGroup: Int = getColumnIndexOrThrow(_stmt, "group")
        val _columnIndexOfCategory: Int = getColumnIndexOrThrow(_stmt, "category")
        val _columnIndexOfPronounce: Int = getColumnIndexOrThrow(_stmt, "pronounce")
        val _columnIndexOfTag: Int = getColumnIndexOrThrow(_stmt, "tag")
        val _columnIndexOfAvoid: Int = getColumnIndexOrThrow(_stmt, "avoid")
        val _columnIndexOfRemark: Int = getColumnIndexOrThrow(_stmt, "remark")
        val _result: MutableList<VocabEntry> = mutableListOf()
        while (_stmt.step()) {
          val _item: VocabEntry
          val _tmpId: Int
          _tmpId = _stmt.getLong(_columnIndexOfId).toInt()
          val _tmpVocab: String
          _tmpVocab = _stmt.getText(_columnIndexOfVocab)
          val _tmpKanji: String
          _tmpKanji = _stmt.getText(_columnIndexOfKanji)
          val _tmpZhTW: String
          _tmpZhTW = _stmt.getText(_columnIndexOfZhTW)
          val _tmpEnUS: String
          _tmpEnUS = _stmt.getText(_columnIndexOfEnUS)
          val _tmpGroup: String
          _tmpGroup = _stmt.getText(_columnIndexOfGroup)
          val _tmpCategory: String
          _tmpCategory = _stmt.getText(_columnIndexOfCategory)
          val _tmpPronounce: String
          _tmpPronounce = _stmt.getText(_columnIndexOfPronounce)
          val _tmpTag: String
          _tmpTag = _stmt.getText(_columnIndexOfTag)
          val _tmpAvoid: String
          _tmpAvoid = _stmt.getText(_columnIndexOfAvoid)
          val _tmpRemark: String
          _tmpRemark = _stmt.getText(_columnIndexOfRemark)
          _item =
              VocabEntry(_tmpId,_tmpVocab,_tmpKanji,_tmpZhTW,_tmpEnUS,_tmpGroup,_tmpCategory,_tmpPronounce,_tmpTag,_tmpAvoid,_tmpRemark)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override fun getVocabsByGroup(group: String): Flow<List<VocabEntry>> {
    val _sql: String = "SELECT * FROM vocab_entries WHERE `group` = ?"
    return createFlow(__db, false, arrayOf("vocab_entries")) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        var _argIndex: Int = 1
        _stmt.bindText(_argIndex, group)
        val _columnIndexOfId: Int = getColumnIndexOrThrow(_stmt, "id")
        val _columnIndexOfVocab: Int = getColumnIndexOrThrow(_stmt, "vocab")
        val _columnIndexOfKanji: Int = getColumnIndexOrThrow(_stmt, "kanji")
        val _columnIndexOfZhTW: Int = getColumnIndexOrThrow(_stmt, "zhTW")
        val _columnIndexOfEnUS: Int = getColumnIndexOrThrow(_stmt, "enUS")
        val _columnIndexOfGroup: Int = getColumnIndexOrThrow(_stmt, "group")
        val _columnIndexOfCategory: Int = getColumnIndexOrThrow(_stmt, "category")
        val _columnIndexOfPronounce: Int = getColumnIndexOrThrow(_stmt, "pronounce")
        val _columnIndexOfTag: Int = getColumnIndexOrThrow(_stmt, "tag")
        val _columnIndexOfAvoid: Int = getColumnIndexOrThrow(_stmt, "avoid")
        val _columnIndexOfRemark: Int = getColumnIndexOrThrow(_stmt, "remark")
        val _result: MutableList<VocabEntry> = mutableListOf()
        while (_stmt.step()) {
          val _item: VocabEntry
          val _tmpId: Int
          _tmpId = _stmt.getLong(_columnIndexOfId).toInt()
          val _tmpVocab: String
          _tmpVocab = _stmt.getText(_columnIndexOfVocab)
          val _tmpKanji: String
          _tmpKanji = _stmt.getText(_columnIndexOfKanji)
          val _tmpZhTW: String
          _tmpZhTW = _stmt.getText(_columnIndexOfZhTW)
          val _tmpEnUS: String
          _tmpEnUS = _stmt.getText(_columnIndexOfEnUS)
          val _tmpGroup: String
          _tmpGroup = _stmt.getText(_columnIndexOfGroup)
          val _tmpCategory: String
          _tmpCategory = _stmt.getText(_columnIndexOfCategory)
          val _tmpPronounce: String
          _tmpPronounce = _stmt.getText(_columnIndexOfPronounce)
          val _tmpTag: String
          _tmpTag = _stmt.getText(_columnIndexOfTag)
          val _tmpAvoid: String
          _tmpAvoid = _stmt.getText(_columnIndexOfAvoid)
          val _tmpRemark: String
          _tmpRemark = _stmt.getText(_columnIndexOfRemark)
          _item =
              VocabEntry(_tmpId,_tmpVocab,_tmpKanji,_tmpZhTW,_tmpEnUS,_tmpGroup,_tmpCategory,_tmpPronounce,_tmpTag,_tmpAvoid,_tmpRemark)
          _result.add(_item)
        }
        _result
      } finally {
        _stmt.close()
      }
    }
  }

  public override suspend fun deleteAll() {
    val _sql: String = "DELETE FROM vocab_entries"
    return performSuspending(__db, false, true) { _connection ->
      val _stmt: SQLiteStatement = _connection.prepare(_sql)
      try {
        _stmt.step()
      } finally {
        _stmt.close()
      }
    }
  }

  public companion object {
    public fun getRequiredConverters(): List<KClass<*>> = emptyList()
  }
}
