package com.vellumcodex.toppa.data

import android.content.Context
import com.vellumcodex.toppa.utility.loadVocabsFromAssets
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first

class VocabRepository(private val vocabDao: VocabDao) {

    // 獲取所有單字
    val allVocabs: Flow<List<VocabEntry>> = vocabDao.getAllVocabs()

    // 根據組別獲取單字
    fun getVocabsByGroup(group: String): Flow<List<VocabEntry>> {
        return vocabDao.getVocabsByGroup(group)
    }

    // 從 JSON 檔案載入資料到資料庫（只在資料庫為空時）
    suspend fun loadVocabsFromJson(context: Context, filename: String = "vocabs.json") {
        // 檢查資料庫是否為空
        val currentVocabs = vocabDao.getAllVocabsList()
        if (currentVocabs.isEmpty()) {
            // 資料庫為空，從 JSON 檔案載入資料
            val vocabs = loadVocabsFromAssets(context, filename)
            vocabDao.insertAll(vocabs)
        }
    }

    // 清空資料庫
    suspend fun clearDatabase() {
        vocabDao.deleteAll()
    }
}