package com.vellumcodex.toppa.ui.practice.vocab

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.vellumcodex.toppa.MainViewModel

private val quizGroupOptions = mapOf(
    "全部" to "All",
    "JLPT N5" to "N5",
    "JLPT N4" to "N4",
    "JLPT N3" to "N3",
    "JLPT N2" to "N2",
    "JLPT N1" to "N1",
    "自訂題組" to "Custom"
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VocabQuizScreen() {

    val viewModelMain: MainViewModel = viewModel()
    val quizGroup by viewModelMain.quizGroup.collectAsState(initial = "All")
    val quizRandom by viewModelMain.quizRandom.collectAsState(initial = true)

    var quizGroupMenuExpanded by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .padding(16.dp)
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        // 題組來源
        Card(
            colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "題組來源",
                        fontSize = 18.sp,
                        modifier = Modifier.weight(1f)
                    )

                    Box(
                        modifier = Modifier.weight(1f)
                    ) {
                        Button(
                            onClick = { quizGroupMenuExpanded = true },
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors()
                        ) {
                            Text(
                                quizGroupOptions.entries.find { it.value == quizGroup }?.key
                                    ?: quizGroup
                            )
                            Icon(
                                Icons.Default.KeyboardArrowDown,
                                contentDescription = null,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }

                        DropdownMenu(
                            expanded = quizGroupMenuExpanded,
                            onDismissRequest = { quizGroupMenuExpanded = false }
                        ) {
                            quizGroupOptions.forEach { (label, value) ->
                                DropdownMenuItem(
                                    text = { Text(label) },
                                    onClick = {
                                        viewModelMain.setQuizGroup(value)
                                        quizGroupMenuExpanded = false
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }

        // 亂序出題
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp)
                .clickable { viewModelMain.setQuizRandom(!quizRandom) },
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "亂序出題",
                    fontSize = 18.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Switch(
                    checked = quizRandom,
                    onCheckedChange = { viewModelMain.setQuizRandom(it) }
                )
            }
        }
    }
}