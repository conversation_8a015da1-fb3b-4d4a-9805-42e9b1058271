package com.vellumcodex.toppa.ui.practice.vocab

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.lifecycle.viewmodel.compose.viewModel
import com.vellumcodex.toppa.MainViewModel

@Composable
fun VocabQuiz() {
    val viewModel: MainViewModel = viewModel()
    val quizVocabs by viewModel.quizVocabs.collectAsState()
}