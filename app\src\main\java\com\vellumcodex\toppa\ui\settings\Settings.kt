import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Remove
import androidx.compose.material3.Card
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.vellumcodex.toppa.MainViewModel

@Composable
fun SettingsScreen() {

    val viewModelMain: MainViewModel = viewModel()
    val autoNext = viewModelMain.autoNext.value
    val autoNextDelay = viewModelMain.autoNextDelay.value

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        SettingsSection(title = "答題設定") {
            Column {
                SwitchSetting(
                    title = "自動下一題",
                    checked = autoNext,
                    onCheckedChange = { viewModelMain.setAutoNext(it) }
                )

                // 僅在自動下一題啟用時顯示
                if (autoNext) {
                    val currentDelaySec = remember(autoNextDelay) { autoNextDelay }

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "多久後到下一題:",
                            style = MaterialTheme.typography.bodyMedium
                        )

                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 減號按鈕
                            IconButton(
                                onClick = {
                                    val step = if (currentDelaySec <= 500) 100 else 250
                                    val newValue = (currentDelaySec - step).coerceAtLeast(100)
                                    viewModelMain.setAutoNextDelay(newValue)
                                }
                            ) {
                                Icon(Icons.Default.Remove, contentDescription = "減少")
                            }

                            // 可點擊輸入的數字顯示
                            Text(
                                text = "%.2f 秒".format(autoNextDelay / 1000.0),
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                modifier = Modifier
                                    .padding(horizontal = 8.dp)
                            )

                            // 加號按鈕
                            IconButton(
                                onClick = {
                                    val step = if (currentDelaySec < 500) 100 else 250
                                    val newValue = (currentDelaySec + step).coerceAtMost(5000)
                                    viewModelMain.setAutoNextDelay(newValue)
                                }
                            ) {
                                Icon(Icons.Default.Add, contentDescription = "增加")
                            }
                        }
                    }
                }
            }
        }
    }

}

@Composable
fun SettingsSection(title: String, content: @Composable () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleLarge,
            modifier = Modifier.padding(vertical = 8.dp)
        )

        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = MaterialTheme.shapes.medium
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                content()
            }
        }
    }
}

@Composable
fun SwitchSetting(
    title: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium
        )
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}