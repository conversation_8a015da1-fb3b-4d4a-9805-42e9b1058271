  	Alignment    Arrangement    Boolean    Button    Card    Column    
Composable    DictionaryScreen    
FontWeight    Icon    
IconButton    Icons    
MainViewModel    
MaterialTheme    Modifier    PracticeScreen    
ProfileScreen    Row    SettingsScreen    SettingsSection    String    Switch    
SwitchSetting    Text    	TextAlign    Unit    
coerceAtLeast    coerceAtMost    fillMaxSize    fillMaxWidth    format    padding    remember    Activity android.app  Application android.app  CoroutineScope android.app.Activity  Dispatchers android.app.Activity  
MainScreen android.app.Activity  	MobileAds android.app.Activity  
ToppaTheme android.app.Activity  enableEdgeToEdge android.app.Activity  launch android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  AppDatabase android.app.Application  CoroutineScope android.app.Application  Dispatchers android.app.Application  SoundEffect android.app.Application  
TTSManager android.app.Application  getDatabase android.app.Application  getValue android.app.Application  
initialize android.app.Application  
initializeTTS android.app.Application  launch android.app.Application  lazy android.app.Application  onCreate android.app.Application  onTerminate android.app.Application  provideDelegate android.app.Application  shutdownTTS android.app.Application  Context android.content  AppDatabase android.content.Context  CoroutineScope android.content.Context  Dispatchers android.content.Context  
MainScreen android.content.Context  	MobileAds android.content.Context  SoundEffect android.content.Context  
TTSManager android.content.Context  
ToppaTheme android.content.Context  applicationContext android.content.Context  assets android.content.Context  enableEdgeToEdge android.content.Context  getDatabase android.content.Context  getValue android.content.Context  
initialize android.content.Context  
initializeTTS android.content.Context  launch android.content.Context  lazy android.content.Context  let android.content.Context  provideDelegate android.content.Context  
setContent android.content.Context  shutdownTTS android.content.Context  AppDatabase android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  
MainScreen android.content.ContextWrapper  	MobileAds android.content.ContextWrapper  SoundEffect android.content.ContextWrapper  
TTSManager android.content.ContextWrapper  
ToppaTheme android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  getDatabase android.content.ContextWrapper  getValue android.content.ContextWrapper  
initialize android.content.ContextWrapper  
initializeTTS android.content.ContextWrapper  launch android.content.ContextWrapper  lazy android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  
setContent android.content.ContextWrapper  shutdownTTS android.content.ContextWrapper  open  android.content.res.AssetManager  	SoundPool 
android.media  Builder android.media.SoundPool  load android.media.SoundPool  play android.media.SoundPool  release android.media.SoundPool  build android.media.SoundPool.Builder  
setMaxStreams android.media.SoundPool.Builder  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  putFloat android.os.Bundle  TextToSpeech android.speech.tts  LANG_AVAILABLE android.speech.tts.TextToSpeech  LANG_MISSING_DATA android.speech.tts.TextToSpeech  LANG_NOT_SUPPORTED android.speech.tts.TextToSpeech  OnInitListener android.speech.tts.TextToSpeech  QUEUE_FLUSH android.speech.tts.TextToSpeech  SUCCESS android.speech.tts.TextToSpeech  isLanguageAvailable android.speech.tts.TextToSpeech  let android.speech.tts.TextToSpeech  setLanguage android.speech.tts.TextToSpeech  shutdown android.speech.tts.TextToSpeech  speak android.speech.tts.TextToSpeech  stop android.speech.tts.TextToSpeech  KEY_PARAM_VOLUME &android.speech.tts.TextToSpeech.Engine  Log android.util  d android.util.Log  CoroutineScope  android.view.ContextThemeWrapper  Dispatchers  android.view.ContextThemeWrapper  
MainScreen  android.view.ContextThemeWrapper  	MobileAds  android.view.ContextThemeWrapper  
ToppaTheme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  CoroutineScope #androidx.activity.ComponentActivity  Dispatchers #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  	MobileAds #androidx.activity.ComponentActivity  
ToppaTheme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  CoroutineScope -androidx.activity.ComponentActivity.Companion  Dispatchers -androidx.activity.ComponentActivity.Companion  
MainScreen -androidx.activity.ComponentActivity.Companion  	MobileAds -androidx.activity.ComponentActivity.Companion  
ToppaTheme -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  launch -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  ScrollState androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Button +androidx.compose.foundation.layout.BoxScope  ButtonDefaults +androidx.compose.foundation.layout.BoxScope  DictionaryScreen +androidx.compose.foundation.layout.BoxScope  DropdownMenu +androidx.compose.foundation.layout.BoxScope  DropdownMenuItem +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  KeyboardArrowDown +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  PracticeScreen +androidx.compose.foundation.layout.BoxScope  
ProfileScreen +androidx.compose.foundation.layout.BoxScope  SettingsScreen +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  VocabQuizScreen +androidx.compose.foundation.layout.BoxScope  buttonColors +androidx.compose.foundation.layout.BoxScope  
component1 +androidx.compose.foundation.layout.BoxScope  
component2 +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  find +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  quizGroupOptions +androidx.compose.foundation.layout.BoxScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  DropdownMenu .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowDown .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Remove .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  SettingsSection .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  
SwitchSetting .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  
coerceAtLeast .androidx.compose.foundation.layout.ColumnScope  coerceAtMost .androidx.compose.foundation.layout.ColumnScope  
component1 .androidx.compose.foundation.layout.ColumnScope  
component2 .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  find .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  quizGroupOptions .androidx.compose.foundation.layout.ColumnScope  remember .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  Book +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  DropdownMenu +androidx.compose.foundation.layout.RowScope  DropdownMenuItem +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  KeyboardArrowDown +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  NavigationBarItem +androidx.compose.foundation.layout.RowScope  Person +androidx.compose.foundation.layout.RowScope  Remove +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  	TextAlign +androidx.compose.foundation.layout.RowScope  
TrendingUp +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  
coerceAtLeast +androidx.compose.foundation.layout.RowScope  coerceAtMost +androidx.compose.foundation.layout.RowScope  
component1 +androidx.compose.foundation.layout.RowScope  
component2 +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fillMaxWidth +androidx.compose.foundation.layout.RowScope  find +androidx.compose.foundation.layout.RowScope  format +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  quizGroupOptions +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  CornerBasedShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  AutoMirrored %androidx.compose.material.icons.Icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Filled 2androidx.compose.material.icons.Icons.AutoMirrored  
TrendingUp 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  Add ,androidx.compose.material.icons.Icons.Filled  ArrowBackIosNew ,androidx.compose.material.icons.Icons.Filled  Book ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowDown ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  Remove ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  
TrendingUp 3androidx.compose.material.icons.automirrored.filled  Add &androidx.compose.material.icons.filled  ArrowBackIosNew &androidx.compose.material.icons.filled  Book &androidx.compose.material.icons.filled  KeyboardArrowDown &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  Remove &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  ColorScheme androidx.compose.material3  DropdownMenu androidx.compose.material3  DropdownMenuItem androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  
MaterialTheme androidx.compose.material3  
NavigationBar androidx.compose.material3  NavigationBarItem androidx.compose.material3  Scaffold androidx.compose.material3  Shapes androidx.compose.material3  Switch androidx.compose.material3  Text androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  onPrimaryContainer &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  shapes (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  medium !androidx.compose.material3.Shapes  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  
bodyMedium %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
Composable androidx.compose.runtime  MutableIntState androidx.compose.runtime  MutableState androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  State androidx.compose.runtime  collectAsState androidx.compose.runtime  getValue androidx.compose.runtime  mutableIntStateOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  intValue (androidx.compose.runtime.MutableIntState  getValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  value %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  value androidx.compose.runtime.State  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  invoke 5androidx.compose.runtime.internal.ComposableFunction0  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  copy "androidx.compose.ui.text.TextStyle  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  CoroutineScope #androidx.core.app.ComponentActivity  Dispatchers #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  	MobileAds #androidx.core.app.ComponentActivity  
ToppaTheme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  AndroidViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  getApplication #androidx.lifecycle.AndroidViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  Dao 
androidx.room  Database 
androidx.room  Entity 
androidx.room  EntityInsertAdapter 
androidx.room  Insert 
androidx.room  InvalidationTracker 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  RoomOpenDelegate 
androidx.room  insert !androidx.room.EntityInsertAdapter  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  AppDatabase androidx.room.RoomDatabase  AutoMigrationSpec androidx.room.RoomDatabase  Builder androidx.room.RoomDatabase  	Companion androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  InvalidationTracker androidx.room.RoomDatabase  KClass androidx.room.RoomDatabase  Lazy androidx.room.RoomDatabase  List androidx.room.RoomDatabase  Map androidx.room.RoomDatabase  	Migration androidx.room.RoomDatabase  MutableList androidx.room.RoomDatabase  
MutableMap androidx.room.RoomDatabase  
MutableSet androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  RoomOpenDelegate androidx.room.RoomDatabase  SQLiteConnection androidx.room.RoomDatabase  Set androidx.room.RoomDatabase  String androidx.room.RoomDatabase  	TableInfo androidx.room.RoomDatabase  VocabDao androidx.room.RoomDatabase  
VocabDao_Impl androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  databaseBuilder androidx.room.RoomDatabase  dropFtsSyncTriggers androidx.room.RoomDatabase  execSQL androidx.room.RoomDatabase  getRequiredConverters androidx.room.RoomDatabase  internalInitInvalidationTracker androidx.room.RoomDatabase  java androidx.room.RoomDatabase  lazy androidx.room.RoomDatabase  
mutableListOf androidx.room.RoomDatabase  mutableMapOf androidx.room.RoomDatabase  mutableSetOf androidx.room.RoomDatabase  performClear androidx.room.RoomDatabase  read androidx.room.RoomDatabase  synchronized androidx.room.RoomDatabase  
trimMargin androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  fallbackToDestructiveMigration "androidx.room.RoomDatabase.Builder  AppDatabase $androidx.room.RoomDatabase.Companion  InvalidationTracker $androidx.room.RoomDatabase.Companion  Room $androidx.room.RoomDatabase.Companion  RoomOpenDelegate $androidx.room.RoomDatabase.Companion  	TableInfo $androidx.room.RoomDatabase.Companion  VocabDao $androidx.room.RoomDatabase.Companion  
VocabDao_Impl $androidx.room.RoomDatabase.Companion  databaseBuilder $androidx.room.RoomDatabase.Companion  dropFtsSyncTriggers $androidx.room.RoomDatabase.Companion  execSQL $androidx.room.RoomDatabase.Companion  getRequiredConverters $androidx.room.RoomDatabase.Companion  internalInitInvalidationTracker $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  lazy $androidx.room.RoomDatabase.Companion  
mutableListOf $androidx.room.RoomDatabase.Companion  mutableMapOf $androidx.room.RoomDatabase.Companion  mutableSetOf $androidx.room.RoomDatabase.Companion  read $androidx.room.RoomDatabase.Companion  synchronized $androidx.room.RoomDatabase.Companion  
trimMargin $androidx.room.RoomDatabase.Companion  ValidationResult +androidx.room.RoomDatabase.RoomOpenDelegate  Column $androidx.room.RoomDatabase.TableInfo  
ForeignKey $androidx.room.RoomDatabase.TableInfo  Index $androidx.room.RoomDatabase.TableInfo  
MutableMap androidx.room.RoomOpenDelegate  
MutableSet androidx.room.RoomOpenDelegate  RoomOpenDelegate androidx.room.RoomOpenDelegate  SQLiteConnection androidx.room.RoomOpenDelegate  String androidx.room.RoomOpenDelegate  	TableInfo androidx.room.RoomOpenDelegate  ValidationResult androidx.room.RoomOpenDelegate  dropFtsSyncTriggers androidx.room.RoomOpenDelegate  execSQL androidx.room.RoomOpenDelegate  internalInitInvalidationTracker androidx.room.RoomOpenDelegate  mutableMapOf androidx.room.RoomOpenDelegate  mutableSetOf androidx.room.RoomOpenDelegate  read androidx.room.RoomOpenDelegate  
trimMargin androidx.room.RoomOpenDelegate  ValidationResult /androidx.room.RoomOpenDelegate.RoomOpenDelegate  Column (androidx.room.RoomOpenDelegate.TableInfo  
ForeignKey (androidx.room.RoomOpenDelegate.TableInfo  Index (androidx.room.RoomOpenDelegate.TableInfo  
createFlow androidx.room.coroutines  AutoMigrationSpec androidx.room.migration  	Migration androidx.room.migration  	TableInfo androidx.room.util  dropFtsSyncTriggers androidx.room.util  getColumnIndexOrThrow androidx.room.util  performSuspending androidx.room.util  CREATED_FROM_ENTITY androidx.room.util.TableInfo  Column androidx.room.util.TableInfo  	Companion androidx.room.util.TableInfo  
ForeignKey androidx.room.util.TableInfo  Index androidx.room.util.TableInfo  equals androidx.room.util.TableInfo  CREATED_FROM_ENTITY &androidx.room.util.TableInfo.Companion  read &androidx.room.util.TableInfo.Companion  SQLiteConnection androidx.sqlite  SQLiteStatement androidx.sqlite  execSQL androidx.sqlite  execSQL  androidx.sqlite.SQLiteConnection  prepare  androidx.sqlite.SQLiteConnection  bindLong androidx.sqlite.SQLiteStatement  bindText androidx.sqlite.SQLiteStatement  close androidx.sqlite.SQLiteStatement  getLong androidx.sqlite.SQLiteStatement  getText androidx.sqlite.SQLiteStatement  step androidx.sqlite.SQLiteStatement  	MobileAds com.google.android.gms.ads  
initialize $com.google.android.gms.ads.MobileAds  InitializationStatus )com.google.android.gms.ads.initialization   OnInitializationCompleteListener )com.google.android.gms.ads.initialization  <SAM-CONSTRUCTOR> Jcom.google.android.gms.ads.initialization.OnInitializationCompleteListener  Gson com.google.gson  fromJson com.google.gson.Gson  	TypeToken com.google.gson.reflect  type !com.google.gson.reflect.TypeToken  	Alignment com.vellumcodex.toppa  AndroidViewModel com.vellumcodex.toppa  AppDatabase com.vellumcodex.toppa  Application com.vellumcodex.toppa  Boolean com.vellumcodex.toppa  Bundle com.vellumcodex.toppa  ComponentActivity com.vellumcodex.toppa  
Composable com.vellumcodex.toppa  CoroutineScope com.vellumcodex.toppa  DictionaryScreen com.vellumcodex.toppa  Dispatchers com.vellumcodex.toppa  ExperimentalMaterial3Api com.vellumcodex.toppa  Icon com.vellumcodex.toppa  Icons com.vellumcodex.toppa  Int com.vellumcodex.toppa  List com.vellumcodex.toppa  MainActivity com.vellumcodex.toppa  MainApplication com.vellumcodex.toppa  
MainScreen com.vellumcodex.toppa  
MainViewModel com.vellumcodex.toppa  
MaterialTheme com.vellumcodex.toppa  	MobileAds com.vellumcodex.toppa  Modifier com.vellumcodex.toppa  MutableStateFlow com.vellumcodex.toppa  OptIn com.vellumcodex.toppa  PracticeScreen com.vellumcodex.toppa  
ProfileScreen com.vellumcodex.toppa  R com.vellumcodex.toppa  SettingsScreen com.vellumcodex.toppa  SoundEffect com.vellumcodex.toppa  State com.vellumcodex.toppa  	StateFlow com.vellumcodex.toppa  String com.vellumcodex.toppa  Text com.vellumcodex.toppa  TopAppBarDefaults com.vellumcodex.toppa  
ToppaTheme com.vellumcodex.toppa  
VocabEntry com.vellumcodex.toppa  VocabQuizScreen com.vellumcodex.toppa  VocabRepository com.vellumcodex.toppa  
_quizGroup com.vellumcodex.toppa  _quizVocabs com.vellumcodex.toppa  	allVocabs com.vellumcodex.toppa  coerceIn com.vellumcodex.toppa  	emptyList com.vellumcodex.toppa  fillMaxSize com.vellumcodex.toppa  filter com.vellumcodex.toppa  getApplication com.vellumcodex.toppa  getDatabase com.vellumcodex.toppa  getValue com.vellumcodex.toppa  
initialize com.vellumcodex.toppa  launch com.vellumcodex.toppa  lazy com.vellumcodex.toppa  mutableIntStateOf com.vellumcodex.toppa  mutableStateOf com.vellumcodex.toppa  provideDelegate com.vellumcodex.toppa  
repository com.vellumcodex.toppa  topAppBarColors com.vellumcodex.toppa  CoroutineScope "com.vellumcodex.toppa.MainActivity  Dispatchers "com.vellumcodex.toppa.MainActivity  
MainScreen "com.vellumcodex.toppa.MainActivity  	MobileAds "com.vellumcodex.toppa.MainActivity  
ToppaTheme "com.vellumcodex.toppa.MainActivity  enableEdgeToEdge "com.vellumcodex.toppa.MainActivity  launch "com.vellumcodex.toppa.MainActivity  
setContent "com.vellumcodex.toppa.MainActivity  AppDatabase %com.vellumcodex.toppa.MainApplication  CoroutineScope %com.vellumcodex.toppa.MainApplication  Dispatchers %com.vellumcodex.toppa.MainApplication  SoundEffect %com.vellumcodex.toppa.MainApplication  database %com.vellumcodex.toppa.MainApplication  getDatabase %com.vellumcodex.toppa.MainApplication  getValue %com.vellumcodex.toppa.MainApplication  
initialize %com.vellumcodex.toppa.MainApplication  launch %com.vellumcodex.toppa.MainApplication  lazy %com.vellumcodex.toppa.MainApplication  provideDelegate %com.vellumcodex.toppa.MainApplication  MutableStateFlow #com.vellumcodex.toppa.MainViewModel  VocabRepository #com.vellumcodex.toppa.MainViewModel  	_autoNext #com.vellumcodex.toppa.MainViewModel  _autoNextDelay #com.vellumcodex.toppa.MainViewModel  
_quizGroup #com.vellumcodex.toppa.MainViewModel  _quizRandom #com.vellumcodex.toppa.MainViewModel  _quizVocabs #com.vellumcodex.toppa.MainViewModel  _screenMain #com.vellumcodex.toppa.MainViewModel  _screenPractice #com.vellumcodex.toppa.MainViewModel  	allVocabs #com.vellumcodex.toppa.MainViewModel  autoNext #com.vellumcodex.toppa.MainViewModel  
autoNextDelay #com.vellumcodex.toppa.MainViewModel  coerceIn #com.vellumcodex.toppa.MainViewModel  	emptyList #com.vellumcodex.toppa.MainViewModel  filter #com.vellumcodex.toppa.MainViewModel  getApplication #com.vellumcodex.toppa.MainViewModel  launch #com.vellumcodex.toppa.MainViewModel  mutableIntStateOf #com.vellumcodex.toppa.MainViewModel  mutableStateOf #com.vellumcodex.toppa.MainViewModel  	quizGroup #com.vellumcodex.toppa.MainViewModel  
quizRandom #com.vellumcodex.toppa.MainViewModel  
repository #com.vellumcodex.toppa.MainViewModel  
screenMain #com.vellumcodex.toppa.MainViewModel  screenPractice #com.vellumcodex.toppa.MainViewModel  setAutoNext #com.vellumcodex.toppa.MainViewModel  setAutoNextDelay #com.vellumcodex.toppa.MainViewModel  
setMainScreen #com.vellumcodex.toppa.MainViewModel  setPracticeScreen #com.vellumcodex.toppa.MainViewModel  setQuizGroup #com.vellumcodex.toppa.MainViewModel  
setQuizRandom #com.vellumcodex.toppa.MainViewModel  viewModelScope #com.vellumcodex.toppa.MainViewModel  correct_answer com.vellumcodex.toppa.R.raw  wrong_answer com.vellumcodex.toppa.R.raw  AppDatabase com.vellumcodex.toppa.data  AppDatabase_Impl com.vellumcodex.toppa.data  AutoMigrationSpec com.vellumcodex.toppa.data  Context com.vellumcodex.toppa.data  Dao com.vellumcodex.toppa.data  Database com.vellumcodex.toppa.data  Entity com.vellumcodex.toppa.data  EntityInsertAdapter com.vellumcodex.toppa.data  Flow com.vellumcodex.toppa.data  	Generated com.vellumcodex.toppa.data  Insert com.vellumcodex.toppa.data  Int com.vellumcodex.toppa.data  InvalidationTracker com.vellumcodex.toppa.data  KClass com.vellumcodex.toppa.data  Lazy com.vellumcodex.toppa.data  List com.vellumcodex.toppa.data  Map com.vellumcodex.toppa.data  	Migration com.vellumcodex.toppa.data  MutableList com.vellumcodex.toppa.data  
MutableMap com.vellumcodex.toppa.data  
MutableSet com.vellumcodex.toppa.data  OnConflictStrategy com.vellumcodex.toppa.data  
PrimaryKey com.vellumcodex.toppa.data  Query com.vellumcodex.toppa.data  Room com.vellumcodex.toppa.data  RoomDatabase com.vellumcodex.toppa.data  RoomOpenDelegate com.vellumcodex.toppa.data  SQLiteConnection com.vellumcodex.toppa.data  SQLiteStatement com.vellumcodex.toppa.data  Set com.vellumcodex.toppa.data  String com.vellumcodex.toppa.data  Suppress com.vellumcodex.toppa.data  	TableInfo com.vellumcodex.toppa.data  Unit com.vellumcodex.toppa.data  VocabDao com.vellumcodex.toppa.data  
VocabDao_Impl com.vellumcodex.toppa.data  
VocabEntry com.vellumcodex.toppa.data  VocabRepository com.vellumcodex.toppa.data  Volatile com.vellumcodex.toppa.data  arrayOf com.vellumcodex.toppa.data  
createFlow com.vellumcodex.toppa.data  databaseBuilder com.vellumcodex.toppa.data  dropFtsSyncTriggers com.vellumcodex.toppa.data  	emptyList com.vellumcodex.toppa.data  execSQL com.vellumcodex.toppa.data  getColumnIndexOrThrow com.vellumcodex.toppa.data  getRequiredConverters com.vellumcodex.toppa.data  internalInitInvalidationTracker com.vellumcodex.toppa.data  java com.vellumcodex.toppa.data  lazy com.vellumcodex.toppa.data  loadVocabsFromAssets com.vellumcodex.toppa.data  
mutableListOf com.vellumcodex.toppa.data  mutableMapOf com.vellumcodex.toppa.data  mutableSetOf com.vellumcodex.toppa.data  performSuspending com.vellumcodex.toppa.data  read com.vellumcodex.toppa.data  synchronized com.vellumcodex.toppa.data  
trimMargin com.vellumcodex.toppa.data  AppDatabase &com.vellumcodex.toppa.data.AppDatabase  AutoMigrationSpec &com.vellumcodex.toppa.data.AppDatabase  	Companion &com.vellumcodex.toppa.data.AppDatabase  Context &com.vellumcodex.toppa.data.AppDatabase  INSTANCE &com.vellumcodex.toppa.data.AppDatabase  InvalidationTracker &com.vellumcodex.toppa.data.AppDatabase  KClass &com.vellumcodex.toppa.data.AppDatabase  Lazy &com.vellumcodex.toppa.data.AppDatabase  List &com.vellumcodex.toppa.data.AppDatabase  Map &com.vellumcodex.toppa.data.AppDatabase  	Migration &com.vellumcodex.toppa.data.AppDatabase  MutableList &com.vellumcodex.toppa.data.AppDatabase  
MutableMap &com.vellumcodex.toppa.data.AppDatabase  
MutableSet &com.vellumcodex.toppa.data.AppDatabase  Room &com.vellumcodex.toppa.data.AppDatabase  RoomOpenDelegate &com.vellumcodex.toppa.data.AppDatabase  SQLiteConnection &com.vellumcodex.toppa.data.AppDatabase  Set &com.vellumcodex.toppa.data.AppDatabase  String &com.vellumcodex.toppa.data.AppDatabase  	TableInfo &com.vellumcodex.toppa.data.AppDatabase  VocabDao &com.vellumcodex.toppa.data.AppDatabase  
VocabDao_Impl &com.vellumcodex.toppa.data.AppDatabase  Volatile &com.vellumcodex.toppa.data.AppDatabase  databaseBuilder &com.vellumcodex.toppa.data.AppDatabase  dropFtsSyncTriggers &com.vellumcodex.toppa.data.AppDatabase  execSQL &com.vellumcodex.toppa.data.AppDatabase  getDatabase &com.vellumcodex.toppa.data.AppDatabase  getRequiredConverters &com.vellumcodex.toppa.data.AppDatabase  internalInitInvalidationTracker &com.vellumcodex.toppa.data.AppDatabase  java &com.vellumcodex.toppa.data.AppDatabase  lazy &com.vellumcodex.toppa.data.AppDatabase  
mutableListOf &com.vellumcodex.toppa.data.AppDatabase  mutableMapOf &com.vellumcodex.toppa.data.AppDatabase  mutableSetOf &com.vellumcodex.toppa.data.AppDatabase  performClear &com.vellumcodex.toppa.data.AppDatabase  read &com.vellumcodex.toppa.data.AppDatabase  synchronized &com.vellumcodex.toppa.data.AppDatabase  
trimMargin &com.vellumcodex.toppa.data.AppDatabase  vocabDao &com.vellumcodex.toppa.data.AppDatabase  AppDatabase 0com.vellumcodex.toppa.data.AppDatabase.Companion  INSTANCE 0com.vellumcodex.toppa.data.AppDatabase.Companion  InvalidationTracker 0com.vellumcodex.toppa.data.AppDatabase.Companion  Room 0com.vellumcodex.toppa.data.AppDatabase.Companion  RoomOpenDelegate 0com.vellumcodex.toppa.data.AppDatabase.Companion  	TableInfo 0com.vellumcodex.toppa.data.AppDatabase.Companion  VocabDao 0com.vellumcodex.toppa.data.AppDatabase.Companion  
VocabDao_Impl 0com.vellumcodex.toppa.data.AppDatabase.Companion  databaseBuilder 0com.vellumcodex.toppa.data.AppDatabase.Companion  dropFtsSyncTriggers 0com.vellumcodex.toppa.data.AppDatabase.Companion  execSQL 0com.vellumcodex.toppa.data.AppDatabase.Companion  getDatabase 0com.vellumcodex.toppa.data.AppDatabase.Companion  getRequiredConverters 0com.vellumcodex.toppa.data.AppDatabase.Companion  internalInitInvalidationTracker 0com.vellumcodex.toppa.data.AppDatabase.Companion  java 0com.vellumcodex.toppa.data.AppDatabase.Companion  lazy 0com.vellumcodex.toppa.data.AppDatabase.Companion  
mutableListOf 0com.vellumcodex.toppa.data.AppDatabase.Companion  mutableMapOf 0com.vellumcodex.toppa.data.AppDatabase.Companion  mutableSetOf 0com.vellumcodex.toppa.data.AppDatabase.Companion  read 0com.vellumcodex.toppa.data.AppDatabase.Companion  synchronized 0com.vellumcodex.toppa.data.AppDatabase.Companion  
trimMargin 0com.vellumcodex.toppa.data.AppDatabase.Companion  ValidationResult 7com.vellumcodex.toppa.data.AppDatabase.RoomOpenDelegate  Column 0com.vellumcodex.toppa.data.AppDatabase.TableInfo  
ForeignKey 0com.vellumcodex.toppa.data.AppDatabase.TableInfo  Index 0com.vellumcodex.toppa.data.AppDatabase.TableInfo  InvalidationTracker +com.vellumcodex.toppa.data.AppDatabase_Impl  RoomOpenDelegate +com.vellumcodex.toppa.data.AppDatabase_Impl  	TableInfo +com.vellumcodex.toppa.data.AppDatabase_Impl  VocabDao +com.vellumcodex.toppa.data.AppDatabase_Impl  
VocabDao_Impl +com.vellumcodex.toppa.data.AppDatabase_Impl  	_vocabDao +com.vellumcodex.toppa.data.AppDatabase_Impl  dropFtsSyncTriggers +com.vellumcodex.toppa.data.AppDatabase_Impl  execSQL +com.vellumcodex.toppa.data.AppDatabase_Impl  getRequiredConverters +com.vellumcodex.toppa.data.AppDatabase_Impl  internalInitInvalidationTracker +com.vellumcodex.toppa.data.AppDatabase_Impl  lazy +com.vellumcodex.toppa.data.AppDatabase_Impl  
mutableListOf +com.vellumcodex.toppa.data.AppDatabase_Impl  mutableMapOf +com.vellumcodex.toppa.data.AppDatabase_Impl  mutableSetOf +com.vellumcodex.toppa.data.AppDatabase_Impl  read +com.vellumcodex.toppa.data.AppDatabase_Impl  
trimMargin +com.vellumcodex.toppa.data.AppDatabase_Impl  ValidationResult +com.vellumcodex.toppa.data.RoomOpenDelegate  Column $com.vellumcodex.toppa.data.TableInfo  
ForeignKey $com.vellumcodex.toppa.data.TableInfo  Index $com.vellumcodex.toppa.data.TableInfo  OnConflictStrategy #com.vellumcodex.toppa.data.VocabDao  	deleteAll #com.vellumcodex.toppa.data.VocabDao  getAllVocabs #com.vellumcodex.toppa.data.VocabDao  getAllVocabsList #com.vellumcodex.toppa.data.VocabDao  getVocabsByGroup #com.vellumcodex.toppa.data.VocabDao  	insertAll #com.vellumcodex.toppa.data.VocabDao  	Companion (com.vellumcodex.toppa.data.VocabDao_Impl  EntityInsertAdapter (com.vellumcodex.toppa.data.VocabDao_Impl  Flow (com.vellumcodex.toppa.data.VocabDao_Impl  Int (com.vellumcodex.toppa.data.VocabDao_Impl  KClass (com.vellumcodex.toppa.data.VocabDao_Impl  List (com.vellumcodex.toppa.data.VocabDao_Impl  MutableList (com.vellumcodex.toppa.data.VocabDao_Impl  RoomDatabase (com.vellumcodex.toppa.data.VocabDao_Impl  SQLiteStatement (com.vellumcodex.toppa.data.VocabDao_Impl  String (com.vellumcodex.toppa.data.VocabDao_Impl  Unit (com.vellumcodex.toppa.data.VocabDao_Impl  
VocabEntry (com.vellumcodex.toppa.data.VocabDao_Impl  __db (com.vellumcodex.toppa.data.VocabDao_Impl  __insertAdapterOfVocabEntry (com.vellumcodex.toppa.data.VocabDao_Impl  arrayOf (com.vellumcodex.toppa.data.VocabDao_Impl  
createFlow (com.vellumcodex.toppa.data.VocabDao_Impl  	emptyList (com.vellumcodex.toppa.data.VocabDao_Impl  getColumnIndexOrThrow (com.vellumcodex.toppa.data.VocabDao_Impl  getRequiredConverters (com.vellumcodex.toppa.data.VocabDao_Impl  
mutableListOf (com.vellumcodex.toppa.data.VocabDao_Impl  performSuspending (com.vellumcodex.toppa.data.VocabDao_Impl  
VocabEntry 2com.vellumcodex.toppa.data.VocabDao_Impl.Companion  arrayOf 2com.vellumcodex.toppa.data.VocabDao_Impl.Companion  
createFlow 2com.vellumcodex.toppa.data.VocabDao_Impl.Companion  	emptyList 2com.vellumcodex.toppa.data.VocabDao_Impl.Companion  getColumnIndexOrThrow 2com.vellumcodex.toppa.data.VocabDao_Impl.Companion  getRequiredConverters 2com.vellumcodex.toppa.data.VocabDao_Impl.Companion  
mutableListOf 2com.vellumcodex.toppa.data.VocabDao_Impl.Companion  performSuspending 2com.vellumcodex.toppa.data.VocabDao_Impl.Companion  avoid %com.vellumcodex.toppa.data.VocabEntry  category %com.vellumcodex.toppa.data.VocabEntry  enUS %com.vellumcodex.toppa.data.VocabEntry  group %com.vellumcodex.toppa.data.VocabEntry  id %com.vellumcodex.toppa.data.VocabEntry  kanji %com.vellumcodex.toppa.data.VocabEntry  	pronounce %com.vellumcodex.toppa.data.VocabEntry  remark %com.vellumcodex.toppa.data.VocabEntry  tag %com.vellumcodex.toppa.data.VocabEntry  vocab %com.vellumcodex.toppa.data.VocabEntry  zhTW %com.vellumcodex.toppa.data.VocabEntry  	allVocabs *com.vellumcodex.toppa.data.VocabRepository  loadVocabsFromAssets *com.vellumcodex.toppa.data.VocabRepository  loadVocabsFromJson *com.vellumcodex.toppa.data.VocabRepository  vocabDao *com.vellumcodex.toppa.data.VocabRepository  	Alignment 'com.vellumcodex.toppa.ui.practice.vocab  Arrangement 'com.vellumcodex.toppa.ui.practice.vocab  Box 'com.vellumcodex.toppa.ui.practice.vocab  Button 'com.vellumcodex.toppa.ui.practice.vocab  ButtonDefaults 'com.vellumcodex.toppa.ui.practice.vocab  Card 'com.vellumcodex.toppa.ui.practice.vocab  CardDefaults 'com.vellumcodex.toppa.ui.practice.vocab  Column 'com.vellumcodex.toppa.ui.practice.vocab  
Composable 'com.vellumcodex.toppa.ui.practice.vocab  DropdownMenu 'com.vellumcodex.toppa.ui.practice.vocab  DropdownMenuItem 'com.vellumcodex.toppa.ui.practice.vocab  ExperimentalMaterial3Api 'com.vellumcodex.toppa.ui.practice.vocab  Icon 'com.vellumcodex.toppa.ui.practice.vocab  Icons 'com.vellumcodex.toppa.ui.practice.vocab  
MainViewModel 'com.vellumcodex.toppa.ui.practice.vocab  
MaterialTheme 'com.vellumcodex.toppa.ui.practice.vocab  Modifier 'com.vellumcodex.toppa.ui.practice.vocab  OptIn 'com.vellumcodex.toppa.ui.practice.vocab  Row 'com.vellumcodex.toppa.ui.practice.vocab  Switch 'com.vellumcodex.toppa.ui.practice.vocab  Text 'com.vellumcodex.toppa.ui.practice.vocab  VocabQuizScreen 'com.vellumcodex.toppa.ui.practice.vocab  buttonColors 'com.vellumcodex.toppa.ui.practice.vocab  
cardColors 'com.vellumcodex.toppa.ui.practice.vocab  	clickable 'com.vellumcodex.toppa.ui.practice.vocab  
component1 'com.vellumcodex.toppa.ui.practice.vocab  
component2 'com.vellumcodex.toppa.ui.practice.vocab  fillMaxWidth 'com.vellumcodex.toppa.ui.practice.vocab  find 'com.vellumcodex.toppa.ui.practice.vocab  forEach 'com.vellumcodex.toppa.ui.practice.vocab  mapOf 'com.vellumcodex.toppa.ui.practice.vocab  padding 'com.vellumcodex.toppa.ui.practice.vocab  provideDelegate 'com.vellumcodex.toppa.ui.practice.vocab  quizGroupOptions 'com.vellumcodex.toppa.ui.practice.vocab  to 'com.vellumcodex.toppa.ui.practice.vocab  weight 'com.vellumcodex.toppa.ui.practice.vocab  Boolean com.vellumcodex.toppa.ui.theme  Build com.vellumcodex.toppa.ui.theme  
Composable com.vellumcodex.toppa.ui.theme  DarkColorScheme com.vellumcodex.toppa.ui.theme  
FontFamily com.vellumcodex.toppa.ui.theme  
FontWeight com.vellumcodex.toppa.ui.theme  LightColorScheme com.vellumcodex.toppa.ui.theme  Pink40 com.vellumcodex.toppa.ui.theme  Pink80 com.vellumcodex.toppa.ui.theme  Purple40 com.vellumcodex.toppa.ui.theme  Purple80 com.vellumcodex.toppa.ui.theme  PurpleGrey40 com.vellumcodex.toppa.ui.theme  PurpleGrey80 com.vellumcodex.toppa.ui.theme  
ToppaTheme com.vellumcodex.toppa.ui.theme  
Typography com.vellumcodex.toppa.ui.theme  Unit com.vellumcodex.toppa.ui.theme  Application com.vellumcodex.toppa.utility  Boolean com.vellumcodex.toppa.utility  Bundle com.vellumcodex.toppa.utility  Context com.vellumcodex.toppa.utility  Float com.vellumcodex.toppa.utility  Int com.vellumcodex.toppa.utility  List com.vellumcodex.toppa.utility  Locale com.vellumcodex.toppa.utility  Log com.vellumcodex.toppa.utility  
MutableMap com.vellumcodex.toppa.utility  R com.vellumcodex.toppa.utility  SoundEffect com.vellumcodex.toppa.utility  	SoundPool com.vellumcodex.toppa.utility  String com.vellumcodex.toppa.utility  TTSApplication com.vellumcodex.toppa.utility  
TTSManager com.vellumcodex.toppa.utility  TextToSpeech com.vellumcodex.toppa.utility  	TypeToken com.vellumcodex.toppa.utility  
VocabEntry com.vellumcodex.toppa.utility  bufferedReader com.vellumcodex.toppa.utility  coerceIn com.vellumcodex.toppa.utility  getValue com.vellumcodex.toppa.utility  
initializeTTS com.vellumcodex.toppa.utility  let com.vellumcodex.toppa.utility  loadVocabsFromAssets com.vellumcodex.toppa.utility  mutableMapOf com.vellumcodex.toppa.utility  mutableStateOf com.vellumcodex.toppa.utility  provideDelegate com.vellumcodex.toppa.utility  readText com.vellumcodex.toppa.utility  set com.vellumcodex.toppa.utility  setValue com.vellumcodex.toppa.utility  shutdownTTS com.vellumcodex.toppa.utility  use com.vellumcodex.toppa.utility  Log )com.vellumcodex.toppa.utility.SoundEffect  R )com.vellumcodex.toppa.utility.SoundEffect  	SoundPool )com.vellumcodex.toppa.utility.SoundEffect  coerceIn )com.vellumcodex.toppa.utility.SoundEffect  enabled )com.vellumcodex.toppa.utility.SoundEffect  
initialize )com.vellumcodex.toppa.utility.SoundEffect  let )com.vellumcodex.toppa.utility.SoundEffect  mutableMapOf )com.vellumcodex.toppa.utility.SoundEffect  set )com.vellumcodex.toppa.utility.SoundEffect  soundMap )com.vellumcodex.toppa.utility.SoundEffect  	soundPool )com.vellumcodex.toppa.utility.SoundEffect  volume )com.vellumcodex.toppa.utility.SoundEffect  
TTSManager ,com.vellumcodex.toppa.utility.TTSApplication  
initializeTTS ,com.vellumcodex.toppa.utility.TTSApplication  shutdownTTS ,com.vellumcodex.toppa.utility.TTSApplication  Bundle (com.vellumcodex.toppa.utility.TTSManager  Locale (com.vellumcodex.toppa.utility.TTSManager  
TTSAutoTry (com.vellumcodex.toppa.utility.TTSManager  	TTSEngine (com.vellumcodex.toppa.utility.TTSManager  
TTSMessage (com.vellumcodex.toppa.utility.TTSManager  TTSName (com.vellumcodex.toppa.utility.TTSManager  TTSState (com.vellumcodex.toppa.utility.TTSManager  TextToSpeech (com.vellumcodex.toppa.utility.TTSManager  
appContext (com.vellumcodex.toppa.utility.TTSManager  coerceIn (com.vellumcodex.toppa.utility.TTSManager  enabled (com.vellumcodex.toppa.utility.TTSManager  getValue (com.vellumcodex.toppa.utility.TTSManager  initListener (com.vellumcodex.toppa.utility.TTSManager  
initializeTTS (com.vellumcodex.toppa.utility.TTSManager  let (com.vellumcodex.toppa.utility.TTSManager  mutableStateOf (com.vellumcodex.toppa.utility.TTSManager  provideDelegate (com.vellumcodex.toppa.utility.TTSManager  setValue (com.vellumcodex.toppa.utility.TTSManager  shutdownTTS (com.vellumcodex.toppa.utility.TTSManager  volume (com.vellumcodex.toppa.utility.TTSManager  OnInitListener *com.vellumcodex.toppa.utility.TextToSpeech  BufferedReader java.io  InputStream java.io  readText java.io.BufferedReader  use java.io.BufferedReader  bufferedReader java.io.InputStream  Class 	java.lang  Locale 	java.util  JAPANESE java.util.Locale  	Generated javax.annotation.processing  Array kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  Int kotlin  Lazy kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  String kotlin  Suppress kotlin  Unit kotlin  arrayOf kotlin  getValue kotlin  lazy kotlin  let kotlin  synchronized kotlin  to kotlin  use kotlin  not kotlin.Boolean  sp 
kotlin.Double  coerceIn kotlin.Float  
coerceAtLeast 
kotlin.Int  coerceAtMost 
kotlin.Int  coerceIn 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  let 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  toLong 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  value kotlin.Lazy  toInt kotlin.Long  format 
kotlin.String  plus 
kotlin.String  to 
kotlin.String  
trimMargin 
kotlin.String  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  find kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  mapOf kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  set kotlin.collections  filter kotlin.collections.List  isEmpty kotlin.collections.List  Entry kotlin.collections.Map  entries kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  key kotlin.collections.Map.Entry  value kotlin.collections.Map.Entry  add kotlin.collections.MutableList  get kotlin.collections.MutableMap  put kotlin.collections.MutableMap  set kotlin.collections.MutableMap  find kotlin.collections.Set  SuspendFunction1 kotlin.coroutines  bufferedReader 	kotlin.io  readText 	kotlin.io  use 	kotlin.io  Volatile 
kotlin.jvm  java 
kotlin.jvm  
coerceAtLeast 
kotlin.ranges  coerceAtMost 
kotlin.ranges  coerceIn 
kotlin.ranges  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  KMutableProperty1 kotlin.reflect  
KProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  filter kotlin.text  find kotlin.text  forEach kotlin.text  format kotlin.text  set kotlin.text  
trimMargin kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  	MobileAds !kotlinx.coroutines.CoroutineScope  
_quizGroup !kotlinx.coroutines.CoroutineScope  _quizVocabs !kotlinx.coroutines.CoroutineScope  	allVocabs !kotlinx.coroutines.CoroutineScope  filter !kotlinx.coroutines.CoroutineScope  getApplication !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  
repository !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  collect kotlinx.coroutines.flow  first kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  forEach    listOf    to    
component1 kotlin.Pair  
component2 kotlin.Pair  listOf kotlin.collections  ExperimentalFoundationApi    	GridCells    OptIn    androidx    height    spacedBy    ExperimentalFoundationApi androidx.compose.foundation  height "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  fillMaxSize +androidx.compose.foundation.layout.RowScope  	GridCells %androidx.compose.foundation.lazy.grid  LazyGridItemScope %androidx.compose.foundation.lazy.grid  
LazyGridScope %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  Adaptive /androidx.compose.foundation.lazy.grid.GridCells  Button 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  Modifier 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  Text 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  	TextAlign 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  dp 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  fillMaxSize 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  fillMaxWidth 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  height 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  padding 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  sp 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  Button 3androidx.compose.foundation.lazy.grid.LazyGridScope  Modifier 3androidx.compose.foundation.lazy.grid.LazyGridScope  Text 3androidx.compose.foundation.lazy.grid.LazyGridScope  	TextAlign 3androidx.compose.foundation.lazy.grid.LazyGridScope  dp 3androidx.compose.foundation.lazy.grid.LazyGridScope  fillMaxSize 3androidx.compose.foundation.lazy.grid.LazyGridScope  fillMaxWidth 3androidx.compose.foundation.lazy.grid.LazyGridScope  height 3androidx.compose.foundation.lazy.grid.LazyGridScope  items 3androidx.compose.foundation.lazy.grid.LazyGridScope  padding 3androidx.compose.foundation.lazy.grid.LazyGridScope  sp 3androidx.compose.foundation.lazy.grid.LazyGridScope  ComposableFunction2 !androidx.compose.runtime.internal  height androidx.compose.ui.Modifier  defaultMinSize    defaultMinSize "androidx.compose.foundation.layout  defaultMinSize 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  defaultMinSize 3androidx.compose.foundation.lazy.grid.LazyGridScope  defaultMinSize androidx.compose.ui.Modifier  Box    wrapContentHeight    wrapContentHeight "androidx.compose.foundation.layout  	TextAlign +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  	Alignment 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  Box 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  wrapContentHeight 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  	Alignment 3androidx.compose.foundation.lazy.grid.LazyGridScope  Box 3androidx.compose.foundation.lazy.grid.LazyGridScope  wrapContentHeight 3androidx.compose.foundation.lazy.grid.LazyGridScope  wrapContentHeight androidx.compose.ui.Modifier  RoundedCornerShape    RoundedCornerShape 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  RoundedCornerShape 3androidx.compose.foundation.lazy.grid.LazyGridScope  RoundedCornerShape !androidx.compose.foundation.shape  aspectRatio    aspectRatio "androidx.compose.foundation.layout  aspectRatio 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  aspectRatio 3androidx.compose.foundation.lazy.grid.LazyGridScope  aspectRatio androidx.compose.ui.Modifier  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  wrapContentHeight .androidx.compose.foundation.layout.ColumnScope  horizontalScroll androidx.compose.foundation  horizontalScroll androidx.compose.ui.Modifier  Spacer    Spacer "androidx.compose.foundation.layout  Spacer .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  height &androidx.compose.ui.Modifier.Companion  println +androidx.compose.foundation.layout.BoxScope  println .androidx.compose.foundation.layout.ColumnScope  println +androidx.compose.foundation.layout.RowScope  
quizVocabs #com.vellumcodex.toppa.MainViewModel  println 'com.vellumcodex.toppa.ui.practice.vocab  println 	kotlin.io  value !kotlinx.coroutines.flow.StateFlow  VocabQuizSetting +androidx.compose.foundation.layout.BoxScope  VocabQuizSetting com.vellumcodex.toppa  VocabQuizSetting 'com.vellumcodex.toppa.ui.practice.vocab  	TextAlign 'com.vellumcodex.toppa.ui.practice.vocab  height 'com.vellumcodex.toppa.ui.practice.vocab  Log .androidx.compose.foundation.layout.ColumnScope  
_isPracticing #com.vellumcodex.toppa.MainViewModel  isPracticing #com.vellumcodex.toppa.MainViewModel  
setPracticing #com.vellumcodex.toppa.MainViewModel  Log 'com.vellumcodex.toppa.ui.practice.vocab  	VocabQuiz 'com.vellumcodex.toppa.ui.practice.vocab  	VocabQuiz +androidx.compose.foundation.layout.BoxScope  	VocabQuiz com.vellumcodex.toppa  
_quizIndex #com.vellumcodex.toppa.MainViewModel  	quizIndex #com.vellumcodex.toppa.MainViewModel  let %com.vellumcodex.toppa.data.VocabEntry  	emptyList 'com.vellumcodex.toppa.ui.practice.vocab  	getOrNull 'com.vellumcodex.toppa.ui.practice.vocab  
isNotEmpty 'com.vellumcodex.toppa.ui.practice.vocab  let 'com.vellumcodex.toppa.ui.practice.vocab  	getOrNull kotlin.collections  
isNotEmpty kotlin.collections  	getOrNull kotlin.collections.List  
isNotEmpty kotlin.collections.List  	getOrNull kotlin.text  
isNotEmpty kotlin.text  VolumeUp 3androidx.compose.material.icons.automirrored.filled  Surface androidx.compose.material3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           