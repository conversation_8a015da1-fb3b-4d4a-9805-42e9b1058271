package com.vellumcodex.toppa.data

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "vocab_entries")
data class VocabEntry(
    @PrimaryKey
    val id: Int,
    val vocab: String,
    val kanji: String,
    val zhTW: String,
    val enUS: String,
    val group: String,
    val category: String,
    val pronounce: String,
    val tag: String,
    val avoid: String,
    val remark: String
)