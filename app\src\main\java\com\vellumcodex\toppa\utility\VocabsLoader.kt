package com.vellumcodex.toppa.utility

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.vellumcodex.toppa.data.VocabEntry

fun loadVocabsFromAssets(context: Context, filename: String = "vocabs.json"): List<VocabEntry> {
    val json = context.assets.open(filename).bufferedReader().use { it.readText() }
    val listType = object : TypeToken<List<VocabEntry>>() {}.type
    return Gson().fromJson(json, listType)
}