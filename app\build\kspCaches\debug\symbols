{"src\\main\\java\\com\\vellumcodex\\toppa\\utility\\SoundEffect.kt": ["SoundEffect:com.vellumcodex.toppa.utility", "release:com.vellumcodex.toppa.utility.SoundEffect", "<init>:com.vellumcodex.toppa.utility.SoundEffect", "initialize:com.vellumcodex.toppa.utility.SoundEffect", "setVolume:com.vellumcodex.toppa.utility.SoundEffect", "play:com.vellumcodex.toppa.utility.SoundEffect", "setEnabled:com.vellumcodex.toppa.utility.SoundEffect"], "src\\main\\java\\com\\vellumcodex\\toppa\\data\\VocabEntry.kt": ["kanji:com.vellumcodex.toppa.data.VocabEntry", "category:com.vellumcodex.toppa.data.VocabEntry", "id:com.vellumcodex.toppa.data.VocabEntry", "zhTW:com.vellumcodex.toppa.data.VocabEntry", "avoid:com.vellumcodex.toppa.data.VocabEntry", "VocabEntry:com.vellumcodex.toppa.data", "group:com.vellumcodex.toppa.data.VocabEntry", "pronounce:com.vellumcodex.toppa.data.VocabEntry", "vocab:com.vellumcodex.toppa.data.VocabEntry", "enUS:com.vellumcodex.toppa.data.VocabEntry", "tag:com.vellumcodex.toppa.data.VocabEntry", "remark:com.vellumcodex.toppa.data.VocabEntry"], "src\\main\\java\\com\\vellumcodex\\toppa\\ui\\theme\\Color.kt": ["Pink40:com.vellumcodex.toppa.ui.theme", "PurpleGrey80:com.vellumcodex.toppa.ui.theme", "Purple40:com.vellumcodex.toppa.ui.theme", "Purple80:com.vellumcodex.toppa.ui.theme", "Pink80:com.vellumcodex.toppa.ui.theme", "PurpleGrey40:com.vellumcodex.toppa.ui.theme"], "src\\main\\java\\com\\vellumcodex\\toppa\\data\\VocabDao.kt": ["getVocabsByGroup:com.vellumcodex.toppa.data.VocabDao", "getAllVocabsList:com.vellumcodex.toppa.data.VocabDao", "insertAll:com.vellumcodex.toppa.data.VocabDao", "VocabDao:com.vellumcodex.toppa.data", "getAllVocabs:com.vellumcodex.toppa.data.VocabDao", "deleteAll:com.vellumcodex.toppa.data.VocabDao"], "src\\main\\java\\com\\vellumcodex\\toppa\\ui\\theme\\Type.kt": ["Typography:com.vellumcodex.toppa.ui.theme"], "src\\main\\java\\com\\vellumcodex\\toppa\\utility\\VocabsLoader.kt": ["loadVocabsFromAssets:com.vellumcodex.toppa.utility"], "src\\main\\java\\com\\vellumcodex\\toppa\\ui\\settings\\Settings.kt": ["SettingsScreen:", "SwitchSetting:", "SettingsSection:"], "src\\main\\java\\com\\vellumcodex\\toppa\\ui\\practice\\vocab\\VocabQuiz.kt": ["VocabQuiz:com.vellumcodex.toppa.ui.practice.vocab"], "src\\main\\java\\com\\vellumcodex\\toppa\\ui\\practice\\Practice.kt": ["PracticeScreen:"], "build\\generated\\ksp\\debug\\kotlin\\com\\vellumcodex\\toppa\\data\\VocabDao_Impl.kt": ["VocabDao_Impl:com.vellumcodex.toppa.data", "Companion:com.vellumcodex.toppa.data.VocabDao_Impl", "getAllVocabsList:com.vellumcodex.toppa.data.VocabDao_Impl", "getVocabsByGroup:com.vellumcodex.toppa.data.VocabDao_Impl", "getAllVocabs:com.vellumcodex.toppa.data.VocabDao_Impl", "<init>:com.vellumcodex.toppa.data.VocabDao_Impl.Companion", "deleteAll:com.vellumcodex.toppa.data.VocabDao_Impl", "insertAll:com.vellumcodex.toppa.data.VocabDao_Impl", "getRequiredConverters:com.vellumcodex.toppa.data.VocabDao_Impl.Companion"], "src\\main\\java\\com\\vellumcodex\\toppa\\MainApplication.kt": ["<init>:com.vellumcodex.toppa.MainApplication", "database:com.vellumcodex.toppa.MainApplication", "onCreate:com.vellumcodex.toppa.MainApplication", "MainApplication:com.vellumcodex.toppa"], "src\\main\\java\\com\\vellumcodex\\toppa\\ui\\profile\\Profile.kt": ["ProfileScreen:"], "src\\main\\java\\com\\vellumcodex\\toppa\\MainViewModel.kt": ["setAutoNextDelay:com.vellumcodex.toppa.MainViewModel", "quizVocabs:com.vellumcodex.toppa.MainViewModel", "setQuizGroup:com.vellumcodex.toppa.MainViewModel", "setMainScreen:com.vellumcodex.toppa.MainViewModel", "quizGroup:com.vellumcodex.toppa.MainViewModel", "setQuizIndex:com.vellumcodex.toppa.MainViewModel", "autoNextDelay:com.vellumcodex.toppa.MainViewModel", "screenMain:com.vellumcodex.toppa.MainViewModel", "setPracticeScreen:com.vellumcodex.toppa.MainViewModel", "setPracticing:com.vellumcodex.toppa.MainViewModel", "isPracticing:com.vellumcodex.toppa.MainViewModel", "setQuizRandom:com.vellumcodex.toppa.MainViewModel", "setAutoNext:com.vellumcodex.toppa.MainViewModel", "quizIndex:com.vellumcodex.toppa.MainViewModel", "autoNext:com.vellumcodex.toppa.MainViewModel", "MainViewModel:com.vellumcodex.toppa", "quizRandom:com.vellumcodex.toppa.MainViewModel", "screenPractice:com.vellumcodex.toppa.MainViewModel"], "src\\main\\java\\com\\vellumcodex\\toppa\\data\\VocabRepository.kt": ["allVocabs:com.vellumcodex.toppa.data.VocabRepository", "loadVocabsFromJson:com.vellumcodex.toppa.data.VocabRepository", "VocabRepository:com.vellumcodex.toppa.data", "getVocabsByGroup:com.vellumcodex.toppa.data.VocabRepository", "clearDatabase:com.vellumcodex.toppa.data.VocabRepository"], "src\\main\\java\\com\\vellumcodex\\toppa\\ui\\theme\\Theme.kt": ["ToppaTheme:com.vellumcodex.toppa.ui.theme"], "src\\main\\java\\com\\vellumcodex\\toppa\\ui\\dictionary\\Dictionary.kt": ["DictionaryScreen:"], "src\\main\\java\\com\\vellumcodex\\toppa\\utility\\TextToSpeech.kt": ["TTSMessage:com.vellumcodex.toppa.utility.TTSManager", "onTerminate:com.vellumcodex.toppa.utility.TTSApplication", "playText:com.vellumcodex.toppa.utility.TTSManager", "shutdownTTS:com.vellumcodex.toppa.utility.TTSManager", "onCreate:com.vellumcodex.toppa.utility.TTSApplication", "setEnabled:com.vellumcodex.toppa.utility.TTSManager", "<init>:com.vellumcodex.toppa.utility.TTSApplication", "TTSState:com.vellumcodex.toppa.utility.TTSManager", "setVolume:com.vellumcodex.toppa.utility.TTSManager", "TTSManager:com.vellumcodex.toppa.utility", "<init>:com.vellumcodex.toppa.utility.TTSManager", "TTSApplication:com.vellumcodex.toppa.utility", "initializeTTS:com.vellumcodex.toppa.utility.TTSManager", "TTSName:com.vellumcodex.toppa.utility.TTSManager"], "src\\main\\java\\com\\vellumcodex\\toppa\\MainActivity.kt": ["onCreate:com.vellumcodex.toppa.MainActivity", "MainScreen:com.vellumcodex.toppa", "<init>:com.vellumcodex.toppa.MainActivity", "MainActivity:com.vellumcodex.toppa"], "build\\generated\\ksp\\debug\\kotlin\\com\\vellumcodex\\toppa\\data\\AppDatabase_Impl.kt": ["getRequiredAutoMigrationSpecClasses:com.vellumcodex.toppa.data.AppDatabase_Impl", "clearAllTables:com.vellumcodex.toppa.data.AppDatabase_Impl", "AppDatabase_Impl:com.vellumcodex.toppa.data", "createInvalidationTracker:com.vellumcodex.toppa.data.AppDatabase_Impl", "getRequiredTypeConverterClasses:com.vellumcodex.toppa.data.AppDatabase_Impl", "<init>:com.vellumcodex.toppa.data.AppDatabase_Impl", "createOpenDelegate:com.vellumcodex.toppa.data.AppDatabase_Impl", "createAutoMigrations:com.vellumcodex.toppa.data.AppDatabase_Impl", "vocabDao:com.vellumcodex.toppa.data.AppDatabase_Impl"], "src\\main\\java\\com\\vellumcodex\\toppa\\data\\AppDatabase.kt": ["getDatabase:com.vellumcodex.toppa.data.AppDatabase.Companion", "Companion:com.vellumcodex.toppa.data.AppDatabase", "<init>:com.vellumcodex.toppa.data.AppDatabase", "AppDatabase:com.vellumcodex.toppa.data", "<init>:com.vellumcodex.toppa.data.AppDatabase.Companion", "vocabDao:com.vellumcodex.toppa.data.AppDatabase"], "src\\main\\java\\com\\vellumcodex\\toppa\\ui\\practice\\vocab\\VocabQuizSetting.kt": ["VocabQuizSetting:com.vellumcodex.toppa.ui.practice.vocab"]}