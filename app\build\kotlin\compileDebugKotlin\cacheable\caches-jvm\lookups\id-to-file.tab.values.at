/ Header Record For PersistentHashMapValueStorageT Sapp/build/generated/ksp/debug/kotlin/com/vellumcodex/toppa/data/AppDatabase_Impl.ktQ Papp/build/generated/ksp/debug/kotlin/com/vellumcodex/toppa/data/VocabDao_Impl.kt8 7app/src/main/java/com/vellumcodex/toppa/MainActivity.kt; :app/src/main/java/com/vellumcodex/toppa/MainApplication.kt9 8app/src/main/java/com/vellumcodex/toppa/MainViewModel.kt< ;app/src/main/java/com/vellumcodex/toppa/data/AppDatabase.kt9 8app/src/main/java/com/vellumcodex/toppa/data/VocabDao.kt; :app/src/main/java/com/vellumcodex/toppa/data/VocabEntry.kt@ ?app/src/main/java/com/vellumcodex/toppa/data/VocabRepository.ktD Capp/src/main/java/com/vellumcodex/toppa/ui/dictionary/Dictionary.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.kt> =app/src/main/java/com/vellumcodex/toppa/ui/profile/Profile.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/settings/Settings.kt: 9app/src/main/java/com/vellumcodex/toppa/ui/theme/Color.kt: 9app/src/main/java/com/vellumcodex/toppa/ui/theme/Theme.kt9 8app/src/main/java/com/vellumcodex/toppa/ui/theme/Type.kt? >app/src/main/java/com/vellumcodex/toppa/utility/SoundEffect.kt@ ?app/src/main/java/com/vellumcodex/toppa/utility/TextToSpeech.kt@ ?app/src/main/java/com/vellumcodex/toppa/utility/VocabsLoader.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.kt8 7app/src/main/java/com/vellumcodex/toppa/MainActivity.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.kt8 7app/src/main/java/com/vellumcodex/toppa/MainActivity.kt9 8app/src/main/java/com/vellumcodex/toppa/MainViewModel.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.kt8 7app/src/main/java/com/vellumcodex/toppa/MainActivity.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.kt@ ?app/src/main/java/com/vellumcodex/toppa/ui/practice/Practice.ktG Fapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuiz.ktN Mapp/src/main/java/com/vellumcodex/toppa/ui/practice/vocab/VocabQuizSetting.kt8 7app/src/main/java/com/vellumcodex/toppa/MainActivity.kt8 7app/src/main/java/com/vellumcodex/toppa/MainActivity.kt