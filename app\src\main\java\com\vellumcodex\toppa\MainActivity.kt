package com.vellumcodex.toppa

import DictionaryScreen
import PracticeScreen
import ProfileScreen
import SettingsScreen
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.TrendingUp
import androidx.compose.material.icons.filled.ArrowBackIosNew
import androidx.compose.material.icons.filled.Book
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.android.gms.ads.MobileAds
import com.vellumcodex.toppa.ui.practice.vocab.VocabQuiz
import com.vellumcodex.toppa.ui.practice.vocab.VocabQuizSetting
import com.vellumcodex.toppa.ui.theme.ToppaTheme
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 使用協程在背景線程初始化 Mobile Ads SDK
        val backgroundScope = CoroutineScope(Dispatchers.IO)
        backgroundScope.launch {
            MobileAds.initialize(this@MainActivity) {
                // 可選的：初始化完成後的回調處理
            }
        }

        setContent {
            ToppaTheme {
                MainScreen()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen() {
    val viewModel: MainViewModel = viewModel()
    val screenMain by viewModel.screenMain.collectAsState(initial = "practice")
    val screenPractice by viewModel.screenPractice.collectAsState(initial = "home")
    val isPracticing by viewModel.isPracticing.collectAsState(initial = false)

    Scaffold(
        modifier = Modifier.fillMaxSize(),

        // 頂部
        topBar = {
            if (screenMain == "practice" && screenPractice == "quiz_vocab") {
                TopAppBar(
                    title = { Text("單字練習") },
                    navigationIcon = {
                        IconButton(onClick = {
                            viewModel.setPracticeScreen("home")
                            viewModel.setPracticing(false)
                        }) {
                            Icon(Icons.Default.ArrowBackIosNew, contentDescription = "返回")
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer,
                        titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                )
            }
        },

        // 底部
        bottomBar = {
            NavigationBar {
                NavigationBarItem(
                    icon = {
                        Icon(
                            Icons.AutoMirrored.Filled.TrendingUp,
                            contentDescription = "突破"
                        )
                    },
                    label = { Text("突破") },
                    selected = screenMain == "practice",
                    onClick = { viewModel.setMainScreen("practice") }
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Book, contentDescription = "字典") },
                    label = { Text("字典") },
                    selected = screenMain == "dictionary",
                    onClick = { viewModel.setMainScreen("dictionary") }
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Person, contentDescription = "個人") },
                    label = { Text("個人") },
                    selected = screenMain == "profile",
                    onClick = { viewModel.setMainScreen("profile") }
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Default.Settings, contentDescription = "設定") },
                    label = { Text("設定") },
                    selected = screenMain == "settings",
                    onClick = { viewModel.setMainScreen("settings") }
                )
            }
        }
    ) { innerPadding ->

        // 主內容
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding),
            contentAlignment = Alignment.Center
        ) {
            when (screenMain) {
                "practice" -> when (screenPractice) {
                    "quiz_vocab" -> if (isPracticing) VocabQuiz() else VocabQuizSetting()
                    else -> PracticeScreen()
                }
                "dictionary" -> DictionaryScreen()
                "profile" -> ProfileScreen()
                "settings" -> SettingsScreen()
                else -> PracticeScreen()
            }
        }
    }
}