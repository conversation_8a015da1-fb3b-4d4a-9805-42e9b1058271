package com.vellumcodex.toppa

import android.app.Application
import com.vellumcodex.toppa.data.AppDatabase
import com.vellumcodex.toppa.utility.SoundEffect
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class MainApplication : Application() {

    // 使用 lazy 延遲初始化資料庫
    val database by lazy { AppDatabase.getDatabase(this) }

    override fun onCreate() {
        super.onCreate()

        // 音效初始化
        SoundEffect.initialize(this)

        // 在背景執行緒初始化資料庫
        CoroutineScope(Dispatchers.IO).launch {}
    }
}